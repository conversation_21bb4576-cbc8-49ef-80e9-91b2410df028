#!/usr/bin/env python3
"""
Demo script for the distributed rate limiting system.
This script demonstrates how to start and use the distributed rate limiting system.
"""

import subprocess
import sys
import time
import requests
import signal
import threading

class DistributedRateLimitDemo:
    def __init__(self):
        self.processes = []
        self.running = True

    def start_main_server(self):
        """Start the main server."""
        print("🚀 Starting main server on port 5001...")
        process = subprocess.Popen([
            sys.executable, "app.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        self.processes.append(process)
        return process

    def start_rate_limit_server(self, port):
        """Start a rate limiting server on the specified port."""
        print(f"🚀 Starting rate limiting server on port {port}...")
        process = subprocess.Popen([
            sys.executable, "rate_limit_server.py",
            "--port", str(port),
            "--main-server", "http://localhost:5001"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        self.processes.append(process)
        return process

    def stop_all_servers(self):
        """Stop all running servers gracefully."""
        print("\n🛑 Stopping all servers...")
        for process in self.processes:
            # Send SIGINT (Ctrl+C) for graceful shutdown
            process.send_signal(signal.SIGINT)
            try:
                process.wait(timeout=5)  # Give more time for graceful shutdown
                print(f"   ✅ Process {process.pid} stopped gracefully")
            except subprocess.TimeoutExpired:
                print(f"   ⚠️  Process {process.pid} didn't stop gracefully, forcing termination")
                process.kill()
        self.processes.clear()
        print("✅ All servers stopped.")

    def wait_for_servers(self, seconds=3):
        """Wait for servers to start up."""
        print(f"⏳ Waiting {seconds} seconds for servers to start...")
        time.sleep(seconds)

    def show_server_status(self):
        """Show the status of registered rate limiting servers."""
        try:
            response = requests.get("http://localhost:5001/rate_limit_servers", timeout=2)
            if response.status_code == 200:
                data = response.json()
                print(f"\n📊 Rate Limiting Servers Status:")
                print(f"   Total registered servers: {data['total_servers']}")
                for i, server in enumerate(data['servers'], 1):
                    print(f"   {i}. Server ID: {server['server_id'][:8]}...")
                    print(f"      URL: {server['url']}")
                    print(f"      Registered: {time.ctime(server['registered_at'])}")
            else:
                print(f"❌ Failed to get server status: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ Error getting server status: {e}")

    def demo_requests(self):
        """Demonstrate rate limiting with different users."""
        print("\n🧪 Demonstrating rate limiting with different users...")
        
        users = ["alice", "bob", "charlie", "diana"]
        
        for user in users:
            print(f"\n👤 Making requests as user '{user}':")
            
            # Make multiple requests to trigger rate limiting
            for i in range(4):
                try:
                    response = requests.post(
                        "http://localhost:5001/",
                        json={"username": user, "message": f"Hello from {user} - request {i+1}"},
                        timeout=5
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        print(f"   ✅ Request {i+1}: SUCCESS - Hash: {data['hash'][:16]}...")
                        if 'debug_info' in data:
                            print(f"      Debug: {data['debug_info']}")
                    elif response.status_code == 429:
                        data = response.json()
                        print(f"   🚫 Request {i+1}: RATE LIMITED - Retry after {data.get('retry_after', 'unknown')}")
                    else:
                        print(f"   ❌ Request {i+1}: ERROR - HTTP {response.status_code}")
                
                except Exception as e:
                    print(f"   ❌ Request {i+1}: FAILED - {e}")
                
                # Small delay between requests
                time.sleep(0.5)

    def show_rate_limit_server_stats(self):
        """Show statistics from rate limiting servers."""
        print("\n📈 Rate Limiting Server Statistics:")
        
        ports = [5002, 5003]
        for port in ports:
            try:
                response = requests.get(f"http://localhost:{port}/stats", timeout=2)
                if response.status_code == 200:
                    data = response.json()
                    print(f"\n   Server on port {port}:")
                    print(f"   Server ID: {data['server_id'][:8]}...")
                    print(f"   Active users: {data['total_active_users']}")
                    
                    if data['user_stats']:
                        print("   User activity:")
                        for username, stats in data['user_stats'].items():
                            print(f"     - {username}: {stats['recent_requests']} recent requests")
                    else:
                        print("   No recent user activity")
                else:
                    print(f"   ❌ Server on port {port}: HTTP {response.status_code}")
            except Exception as e:
                print(f"   ❌ Server on port {port}: {e}")

    def run_demo(self):
        """Run the complete demonstration."""
        print("🎬 Distributed Rate Limiting System Demo")
        print("=" * 50)
        
        try:
            # Start servers
            self.start_main_server()
            self.wait_for_servers(2)
            
            self.start_rate_limit_server(5002)
            self.start_rate_limit_server(5003)
            self.wait_for_servers(3)
            
            # Show initial status
            self.show_server_status()
            
            # Demonstrate rate limiting
            self.demo_requests()
            
            # Show final statistics
            self.show_rate_limit_server_stats()
            
            print("\n🎉 Demo completed successfully!")
            print("\nThe system is now running. You can:")
            print("  - Visit http://localhost:5001/ to see the main server")
            print("  - Visit http://localhost:5001/rate_limit_servers to see registered servers")
            print("  - Visit http://localhost:5002/stats or http://localhost:5003/stats for server stats")
            print("  - Press Ctrl+C to stop all servers")
            
            # Keep running until interrupted
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
            
        finally:
            self.stop_all_servers()

def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully."""
    print("\n\n🛑 Received interrupt signal...")
    demo.running = False

if __name__ == "__main__":
    demo = DistributedRateLimitDemo()
    
    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    
    print("Starting Distributed Rate Limiting Demo...")
    print("Press Ctrl+C to stop all servers and exit.")
    print()
    
    demo.run_demo()
