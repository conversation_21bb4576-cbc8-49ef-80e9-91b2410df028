#!/usr/bin/env python3
"""
Test script for the distributed rate limiting system.
This script tests the interaction between the main server and rate limiting servers.
"""

import requests
import time
import json
import threading
import subprocess
import sys
import signal
import os
from concurrent.futures import ThreadPoolExecutor

class DistributedRateLimitTester:
    def __init__(self):
        self.main_server_url = "http://localhost:5001"
        self.rate_limit_servers = [
            {"port": 5002, "url": "http://localhost:5002"},
            {"port": 5003, "url": "http://localhost:5003"}
        ]
        self.processes = []

    def start_servers(self):
        """Start the main server and rate limiting servers."""
        print("Starting servers...")
        
        # Start main server
        print("Starting main server on port 5001...")
        main_process = subprocess.Popen([
            sys.executable, "app.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        self.processes.append(main_process)
        
        # Wait a bit for main server to start
        time.sleep(2)
        
        # Start rate limiting servers
        for i, server in enumerate(self.rate_limit_servers):
            print(f"Starting rate limiting server {i+1} on port {server['port']}...")
            process = subprocess.Popen([
                sys.executable, "rate_limit_server.py",
                "--port", str(server['port']),
                "--main-server", self.main_server_url
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            self.processes.append(process)
        
        # Wait for all servers to start and register
        time.sleep(5)
        print("All servers started.")

    def stop_servers(self):
        """Stop all running servers gracefully."""
        print("Stopping servers...")
        for process in self.processes:
            # Send SIGINT for graceful shutdown
            process.send_signal(signal.SIGINT)
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print(f"Process {process.pid} didn't stop gracefully, forcing termination")
                process.kill()
        self.processes.clear()
        print("All servers stopped.")

    def test_server_registration(self):
        """Test that rate limiting servers register with the main server."""
        print("\n=== Testing Server Registration ===")
        
        try:
            response = requests.get(f"{self.main_server_url}/rate_limit_servers")
            if response.status_code == 200:
                data = response.json()
                print(f"Registered servers: {data['total_servers']}")
                for server in data['servers']:
                    print(f"  - Server ID: {server['server_id'][:8]}... at {server['url']}")
                
                if data['total_servers'] >= 2:
                    print("✓ Server registration test PASSED")
                    return True
                else:
                    print("✗ Server registration test FAILED - Not enough servers registered")
                    return False
            else:
                print(f"✗ Server registration test FAILED - HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ Server registration test FAILED - {e}")
            return False

    def test_rate_limiting_distribution(self):
        """Test that different users are distributed across different rate limiting servers."""
        print("\n=== Testing Rate Limiting Distribution ===")
        
        test_users = ["user1", "user2", "user3", "user4", "user5"]
        server_usage = {}
        
        try:
            for user in test_users:
                # Make a request to trigger rate limiting check
                response = requests.post(
                    f"{self.main_server_url}/",
                    json={"username": user, "data": "test"},
                    timeout=5
                )
                
                if response.status_code == 200:
                    print(f"✓ Request for {user} succeeded")
                else:
                    print(f"✗ Request for {user} failed with status {response.status_code}")
            
            # Check rate limiting server stats to see distribution
            for server in self.rate_limit_servers:
                try:
                    response = requests.get(f"{server['url']}/stats", timeout=2)
                    if response.status_code == 200:
                        data = response.json()
                        active_users = data.get('total_active_users', 0)
                        server_usage[server['port']] = active_users
                        print(f"Rate limiting server {server['port']}: {active_users} active users")
                except Exception as e:
                    print(f"Could not get stats from server {server['port']}: {e}")
            
            # Check if users are distributed
            total_users_tracked = sum(server_usage.values())
            if total_users_tracked > 0:
                print(f"✓ Distribution test PASSED - {total_users_tracked} users tracked across servers")
                return True
            else:
                print("✗ Distribution test FAILED - No users tracked")
                return False
                
        except Exception as e:
            print(f"✗ Distribution test FAILED - {e}")
            return False

    def test_rate_limiting_enforcement(self):
        """Test that rate limiting is properly enforced."""
        print("\n=== Testing Rate Limiting Enforcement ===")
        
        test_user = "rate_test_user"
        
        try:
            # Make requests rapidly to trigger rate limiting
            success_count = 0
            rate_limited_count = 0
            
            for i in range(5):
                response = requests.post(
                    f"{self.main_server_url}/",
                    json={"username": test_user, "data": f"test_{i}"},
                    timeout=5
                )
                
                if response.status_code == 200:
                    success_count += 1
                    print(f"Request {i+1}: SUCCESS")
                elif response.status_code == 429:
                    rate_limited_count += 1
                    data = response.json()
                    print(f"Request {i+1}: RATE LIMITED (retry after {data.get('retry_after', 'unknown')})")
                else:
                    print(f"Request {i+1}: UNEXPECTED STATUS {response.status_code}")
                
                # Small delay between requests
                time.sleep(0.1)
            
            # We expect some requests to succeed and some to be rate limited
            if success_count > 0 and rate_limited_count > 0:
                print(f"✓ Rate limiting enforcement test PASSED - {success_count} succeeded, {rate_limited_count} rate limited")
                return True
            elif success_count > 0 and rate_limited_count == 0:
                print(f"⚠ Rate limiting enforcement test PARTIAL - {success_count} succeeded, but no rate limiting occurred")
                return True
            else:
                print(f"✗ Rate limiting enforcement test FAILED - {success_count} succeeded, {rate_limited_count} rate limited")
                return False
                
        except Exception as e:
            print(f"✗ Rate limiting enforcement test FAILED - {e}")
            return False

    def test_server_deregistration(self):
        """Test that rate limiting servers can deregister gracefully."""
        print("\n=== Testing Server Deregistration ===")

        try:
            # Get initial server count
            response = requests.get(f"{self.main_server_url}/rate_limit_servers")
            if response.status_code != 200:
                print("✗ Deregistration test FAILED - Could not get initial server list")
                return False

            initial_count = response.json()['total_servers']
            print(f"Initial server count: {initial_count}")

            if initial_count < 2:
                print("✗ Deregistration test FAILED - Need at least 2 servers for this test")
                return False

            # Stop one rate limiting server gracefully (this should trigger deregistration)
            if self.processes and len(self.processes) > 1:
                print("Stopping one rate limiting server gracefully...")
                process_to_stop = self.processes[-1]  # Stop the last rate limiting server
                process_to_stop.send_signal(signal.SIGINT)  # Graceful shutdown
                try:
                    process_to_stop.wait(timeout=5)
                    print("✓ Server stopped gracefully")
                except subprocess.TimeoutExpired:
                    print("⚠️  Server didn't stop gracefully, forcing termination")
                    process_to_stop.kill()
                self.processes.remove(process_to_stop)
                time.sleep(2)  # Give time for deregistration

            # Check if server count decreased
            response = requests.get(f"{self.main_server_url}/rate_limit_servers")
            if response.status_code == 200:
                final_count = response.json()['total_servers']
                print(f"Final server count: {final_count}")

                if final_count == initial_count - 1:
                    print("✓ Deregistration test PASSED - Server count decreased correctly")
                    return True
                else:
                    print(f"✗ Deregistration test FAILED - Expected {initial_count - 1} servers, got {final_count}")
                    return False
            else:
                print("✗ Deregistration test FAILED - Could not get final server list")
                return False

        except Exception as e:
            print(f"✗ Deregistration test FAILED - {e}")
            return False

    def test_fallback_behavior(self):
        """Test fallback behavior when rate limiting servers are unavailable."""
        print("\n=== Testing Fallback Behavior ===")

        # Stop one rate limiting server to test fallback (if not already stopped by deregistration test)
        if self.processes and len(self.processes) > 1:
            print("Stopping one rate limiting server to test fallback...")
            process_to_stop = self.processes[-1]  # Stop the last rate limiting server
            process_to_stop.terminate()
            self.processes.remove(process_to_stop)
            time.sleep(2)

        try:
            # Make a request that should still work with fallback
            response = requests.post(
                f"{self.main_server_url}/",
                json={"username": "fallback_test_user", "data": "test"},
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                if 'debug_info' in data:
                    print(f"✓ Fallback test PASSED - Request succeeded with fallback: {data['debug_info']}")
                else:
                    print("✓ Fallback test PASSED - Request succeeded (may have used remaining server)")
                return True
            else:
                print(f"✗ Fallback test FAILED - Request failed with status {response.status_code}")
                return False

        except Exception as e:
            print(f"✗ Fallback test FAILED - {e}")
            return False

    def run_all_tests(self):
        """Run all tests."""
        print("Starting Distributed Rate Limiting Tests")
        print("=" * 50)
        
        try:
            self.start_servers()
            
            tests = [
                self.test_server_registration,
                self.test_rate_limiting_distribution,
                self.test_rate_limiting_enforcement,
                self.test_server_deregistration,
                self.test_fallback_behavior
            ]
            
            passed = 0
            total = len(tests)
            
            for test in tests:
                if test():
                    passed += 1
                time.sleep(1)  # Brief pause between tests
            
            print("\n" + "=" * 50)
            print(f"Test Results: {passed}/{total} tests passed")
            
            if passed == total:
                print("🎉 All tests PASSED!")
            else:
                print("❌ Some tests FAILED!")
            
            return passed == total
            
        finally:
            self.stop_servers()

if __name__ == "__main__":
    tester = DistributedRateLimitTester()
    
    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        print("\nInterrupted! Stopping servers...")
        tester.stop_servers()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
