#!/usr/bin/env python3
"""
Rate Limiting Server - A dedicated Flask server for handling rate limiting checks.
This server registers with the main server and handles rate limiting for specific users.
"""

from flask import Flask, request, jsonify
import time
import requests
import threading
import uuid
from collections import defaultdict, deque
import argparse
import sys

app = Flask(__name__)

class CustomRateLimiter:
    """Custom rate limiter using sliding window algorithm based on username."""

    def __init__(self):
        # Structure: {username: deque of timestamps}
        self.user_requests = defaultdict(deque)

    def is_allowed(self, username, limit_count, window_seconds):
        """
        Check if a request is allowed for the given username.

        Args:
            username (str): The username to check
            limit_count (int): Maximum number of requests allowed
            window_seconds (int): Time window in seconds

        Returns:
            bool: True if request is allowed, False otherwise
        """
        current_time = time.time()
        user_queue = self.user_requests[username]

        # Remove old requests outside the time window
        while user_queue and current_time - user_queue[0] > window_seconds:
            user_queue.popleft()

        # Check if we're within the limit
        if len(user_queue) < limit_count:
            user_queue.append(current_time)
            return True

        return False

    def get_retry_after(self, username, window_seconds):
        """
        Get the time in seconds until the next request is allowed.

        Args:
            username (str): The username to check
            window_seconds (int): Time window in seconds

        Returns:
            float: Seconds until next request is allowed
        """
        user_queue = self.user_requests[username]
        if not user_queue:
            return 0

        current_time = time.time()
        oldest_request = user_queue[0]
        return max(0, window_seconds - (current_time - oldest_request))

    def get_stats(self):
        """Get statistics about current rate limiting state."""
        current_time = time.time()
        stats = {}
        
        for username, queue in self.user_requests.items():
            # Clean old entries for accurate stats
            while queue and current_time - queue[0] > 300:  # Clean entries older than 5 minutes
                queue.popleft()
            
            if queue:  # Only include users with recent activity
                stats[username] = {
                    'recent_requests': len(queue),
                    'oldest_request_age': current_time - queue[0] if queue else 0,
                    'newest_request_age': current_time - queue[-1] if queue else 0
                }
        
        return stats

# Initialize the rate limiter
rate_limiter = CustomRateLimiter()

# Server configuration
server_id = str(uuid.uuid4())
main_server_url = None
server_port = None

@app.route('/check_rate_limit', methods=['POST'])
def check_rate_limit():
    """
    Check if a request is allowed for the given username.
    
    Expected JSON payload:
    {
        "username": "user123",
        "limit_count": 2,
        "window_seconds": 5
    }
    
    Returns:
    {
        "allowed": true/false,
        "retry_after": seconds (if not allowed),
        "server_id": "uuid"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
        
        username = data.get('username')
        limit_count = data.get('limit_count', 2)
        window_seconds = data.get('window_seconds', 5)
        
        if not username:
            return jsonify({'error': 'Username is required'}), 400
        
        allowed = rate_limiter.is_allowed(username, limit_count, window_seconds)
        
        response = {
            'allowed': allowed,
            'server_id': server_id,
            'username': username
        }
        
        if not allowed:
            retry_after = rate_limiter.get_retry_after(username, window_seconds)
            response['retry_after'] = retry_after
        
        return jsonify(response)
    
    except Exception as e:
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500

@app.route('/stats', methods=['GET'])
def get_stats():
    """Get rate limiting statistics for this server."""
    stats = rate_limiter.get_stats()
    return jsonify({
        'server_id': server_id,
        'total_active_users': len(stats),
        'user_stats': stats,
        'server_info': {
            'port': server_port,
            'main_server_url': main_server_url
        }
    })

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'server_id': server_id,
        'message': 'Rate limiting server is running'
    })

def register_with_main_server():
    """Register this rate limiting server with the main server."""
    if not main_server_url:
        print("No main server URL provided, skipping registration")
        return
    
    registration_data = {
        'server_id': server_id,
        'host': '127.0.0.1',
        'port': server_port,
        'url': f'http://127.0.0.1:{server_port}'
    }
    
    try:
        response = requests.post(
            f'{main_server_url}/register',
            json=registration_data,
            timeout=5
        )
        
        if response.status_code == 200:
            print(f"Successfully registered with main server at {main_server_url}")
            print(f"Server ID: {server_id}")
        else:
            print(f"Failed to register with main server: {response.status_code} - {response.text}")
    
    except requests.exceptions.RequestException as e:
        print(f"Error registering with main server: {e}")

def periodic_registration():
    """Periodically re-register with the main server to maintain connection."""
    while True:
        time.sleep(30)  # Re-register every 30 seconds
        register_with_main_server()

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Rate Limiting Server')
    parser.add_argument('--port', type=int, default=5002, help='Port to run the server on')
    parser.add_argument('--main-server', type=str, help='URL of the main server to register with (e.g., http://localhost:5001)')
    
    args = parser.parse_args()
    
    server_port = args.port
    main_server_url = args.main_server
    
    print(f"Starting Rate Limiting Server...")
    print(f"Server ID: {server_id}")
    print(f"Port: {server_port}")
    
    if main_server_url:
        print(f"Will register with main server at: {main_server_url}")
        # Register immediately on startup
        threading.Timer(2.0, register_with_main_server).start()
        # Start periodic registration in background
        registration_thread = threading.Thread(target=periodic_registration, daemon=True)
        registration_thread.start()
    else:
        print("No main server URL provided, running in standalone mode")
    
    app.run(host='0.0.0.0', port=server_port, debug=False)
