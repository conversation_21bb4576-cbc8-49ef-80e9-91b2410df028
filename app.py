#!/usr/bin/env python3
"""
Flask HTTP server that accepts GET and POST requests and returns a hash of the submitted data.
"""

from flask import Flask, request, jsonify
import hashlib
import json
import time
from functools import lru_cache, wraps
from collections import defaultdict, deque

app = Flask(__name__)

class CustomRateLimiter:
    """Custom rate limiter using sliding window algorithm based on username."""

    def __init__(self):
        # Structure: {username: deque of timestamps}
        self.user_requests = defaultdict(deque)

    def is_allowed(self, username, limit_count, window_seconds):
        """
        Check if a request is allowed for the given username.

        Args:
            username (str): The username to check
            limit_count (int): Maximum number of requests allowed
            window_seconds (int): Time window in seconds

        Returns:
            bool: True if request is allowed, False otherwise
        """
        current_time = time.time()
        user_queue = self.user_requests[username]

        # Remove old requests outside the time window
        while user_queue and current_time - user_queue[0] > window_seconds:
            user_queue.popleft()

        # Check if we're within the limit
        if len(user_queue) < limit_count:
            user_queue.append(current_time)
            return True

        return False

    def get_retry_after(self, username, window_seconds):
        """
        Get the time in seconds until the next request is allowed.

        Args:
            username (str): The username to check
            window_seconds (int): Time window in seconds

        Returns:
            float: Seconds until next request is allowed
        """
        user_queue = self.user_requests[username]
        if not user_queue:
            return 0

        current_time = time.time()
        oldest_request = user_queue[0]
        return max(0, window_seconds - (current_time - oldest_request))

# Initialize the custom rate limiter
rate_limiter = CustomRateLimiter()

def get_username_from_request():
    """
    Extract username from the request payload.

    Returns:
        str: Username if found, otherwise 'anonymous' or IP address as fallback
    """
    username = None

    # Try to get username from JSON data
    if request.is_json:
        data = request.get_json()
        if data and isinstance(data, dict):
            username = data.get('username')

    # Try to get username from form data if not found in JSON
    if not username and request.form:
        username = request.form.get('username')

    # Try to get username from query parameters for GET requests
    if not username and request.args:
        username = request.args.get('username')

    # Fallback to IP address if no username found
    if not username:
        username = request.remote_addr or 'anonymous'

    return username

def rate_limit(limit_count, window_seconds):
    """
    Decorator for rate limiting based on username.

    Args:
        limit_count (int): Maximum number of requests allowed
        window_seconds (int): Time window in seconds
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            username = get_username_from_request()

            if not rate_limiter.is_allowed(username, limit_count, window_seconds):
                retry_after = rate_limiter.get_retry_after(username, window_seconds)
                return jsonify({
                    'error': 'Rate limit exceeded',
                    'message': f'Too many requests for user "{username}". Please wait before making another request.',
                    'retry_after': f'{retry_after:.1f} seconds',
                    'username': username
                }), 429

            return f(*args, **kwargs)
        return decorated_function
    return decorator

# In-memory cache for storing computed hashes
# Structure: {data_key: {"hash": hash_value, "timestamp": creation_time}}
hash_cache = {}
CACHE_TTL = 300  # Cache time-to-live in seconds (5 minutes)

def clean_expired_cache():
    """Remove expired entries from the cache."""
    current_time = time.time()
    expired_keys = [
        key for key, value in hash_cache.items()
        if current_time - value["timestamp"] > CACHE_TTL
    ]
    for key in expired_keys:
        del hash_cache[key]

def get_cache_key(data):
    """Generate a consistent cache key from the data."""
    if isinstance(data, dict):
        # Convert dict to JSON string for consistent key generation
        return json.dumps(data, sort_keys=True)
    elif isinstance(data, str):
        return data
    else:
        return str(data)

def generate_hash_with_cache_info(data):
    """Generate SHA-256 hash of the provided data with caching and return cache info."""
    # Clean expired cache entries periodically
    clean_expired_cache()

    # Generate cache key
    cache_key = get_cache_key(data)

    # Check if hash is already cached
    if cache_key in hash_cache:
        cached_entry = hash_cache[cache_key]
        # Check if cache entry is still valid
        if time.time() - cached_entry["timestamp"] <= CACHE_TTL:
            return cached_entry["hash"], True  # Hash, is_cached
        else:
            # Remove expired entry
            del hash_cache[cache_key]

    # Generate new hash
    if isinstance(data, dict):
        # Convert dict to JSON string for consistent hashing
        data_str = json.dumps(data, sort_keys=True)
    elif isinstance(data, str):
        data_str = data
    else:
        data_str = str(data)

    hash_value = hashlib.sha256(data_str.encode('utf-8')).hexdigest()

    # Store in cache
    hash_cache[cache_key] = {
        "hash": hash_value,
        "timestamp": time.time()
    }

    return hash_value, False  # Hash, is_cached

@app.route('/', methods=['GET', 'POST'])
@rate_limit(2, 5)  # 2 requests per 5 seconds
def hash_data():
    """Handle both GET and POST requests and return hash of the data."""
    
    if request.method == 'GET':
        # For GET requests, hash the query parameters
        data = dict(request.args)
        if not data:
            data = "No query parameters provided"

        hash_value, is_cached = generate_hash_with_cache_info(data)

        return jsonify({
            'method': 'GET',
            'data': data,
            'hash': hash_value,
            'hash_algorithm': 'SHA-256',
            'cached': is_cached,
            'cache_info': 'Result retrieved from cache' if is_cached else 'Result computed and cached'
        })
    
    elif request.method == 'POST':
        # For POST requests, try to get JSON data first, then form data, then raw data
        if request.is_json:
            data = request.get_json()
        elif request.form:
            data = dict(request.form)
        else:
            data = request.get_data(as_text=True)
            if not data:
                data = "No data provided"

        hash_value, is_cached = generate_hash_with_cache_info(data)

        return jsonify({
            'method': 'POST',
            'data': data,
            'hash': hash_value,
            'hash_algorithm': 'SHA-256',
            'cached': is_cached,
            'cache_info': 'Result retrieved from cache' if is_cached else 'Result computed and cached'
        })

@app.route('/health', methods=['GET'])
@rate_limit(10, 5)  # 10 requests per 5 seconds - more lenient for health checks
def health_check():
    """Simple health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'message': 'Flask HTTP server is running'
    })

@app.route('/cache/stats', methods=['GET'])
@rate_limit(5, 5)  # 5 requests per 5 seconds
def cache_stats():
    """Get cache statistics."""
    # Clean expired entries before reporting stats
    clean_expired_cache()

    current_time = time.time()
    cache_entries = []

    for key, value in hash_cache.items():
        age_seconds = current_time - value["timestamp"]
        cache_entries.append({
            "key_preview": key[:50] + "..." if len(key) > 50 else key,
            "hash_preview": value["hash"][:16] + "...",
            "age_seconds": round(age_seconds, 2),
            "expires_in_seconds": round(CACHE_TTL - age_seconds, 2)
        })

    return jsonify({
        'cache_stats': {
            'total_entries': len(hash_cache),
            'cache_ttl_seconds': CACHE_TTL,
            'entries': cache_entries
        }
    })

@app.errorhandler(429)
def ratelimit_handler(error):
    """Handle rate limit exceeded errors."""
    # This error handler is now mainly for any other 429 errors
    # Our custom rate limiter handles its own error responses
    return jsonify({
        'error': 'Rate limit exceeded',
        'message': 'Too many requests. Please wait before making another request.',
        'retry_after': 'a few seconds'
    }), 429



if __name__ == '__main__':
    print("Starting Flask HTTP server...")
    # print("Server will be available at: http://localhost:5000")
    # print("Health check endpoint: http://localhost:5000/health")

    app.run(host='0.0.0.0', port=5001, debug=False)
