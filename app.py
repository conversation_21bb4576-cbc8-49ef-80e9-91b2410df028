#!/usr/bin/env python3
"""
Flask HTTP server that accepts GET and POST requests and returns a hash of the submitted data.
"""

from flask import Flask, request, jsonify
import hashlib
import json
import time
import requests
from functools import wraps
from collections import defaultdict, deque

app = Flask(__name__)

# Rate limiting servers registry
rate_limit_servers = []  # List of registered rate limiting servers

def get_rate_limit_server_for_user(username):
    """
    Select a rate limiting server based on the hash of the username.

    Args:
        username (str): The username to hash

    Returns:
        dict: Rate limiting server info, or None if no servers available
    """
    if not rate_limit_servers:
        return None

    # Hash the username and use modulo to select a server
    username_hash = hashlib.sha256(username.encode('utf-8')).hexdigest()
    server_index = int(username_hash, 16) % len(rate_limit_servers)
    return rate_limit_servers[server_index]

def check_rate_limit_with_server(username, limit_count, window_seconds):
    """
    Check rate limit using the distributed rate limiting system.

    Args:
        username (str): The username to check
        limit_count (int): Maximum number of requests allowed
        window_seconds (int): Time window in seconds

    Returns:
        tuple: (is_allowed: bool, retry_after: float, error_message: str)
    """
    server = get_rate_limit_server_for_user(username)

    if not server:
        # Fallback to local rate limiting if no servers available
        return rate_limiter.is_allowed(username, limit_count, window_seconds), \
               rate_limiter.get_retry_after(username, window_seconds), \
               None

    try:
        response = requests.post(
            f"{server['url']}/check_rate_limit",
            json={
                'username': username,
                'limit_count': limit_count,
                'window_seconds': window_seconds
            },
            timeout=2
        )

        if response.status_code == 200:
            data = response.json()
            return data.get('allowed', False), data.get('retry_after', 0), None
        else:
            # Fallback to local rate limiting on server error
            return rate_limiter.is_allowed(username, limit_count, window_seconds), \
                   rate_limiter.get_retry_after(username, window_seconds), \
                   f"Rate limit server error: {response.status_code}"

    except requests.exceptions.RequestException as e:
        # Fallback to local rate limiting on network error
        return rate_limiter.is_allowed(username, limit_count, window_seconds), \
               rate_limiter.get_retry_after(username, window_seconds), \
               f"Rate limit server unreachable: {str(e)}"

class CustomRateLimiter:
    """Custom rate limiter using sliding window algorithm based on username."""

    def __init__(self):
        # Structure: {username: deque of timestamps}
        self.user_requests = defaultdict(deque)

    def is_allowed(self, username, limit_count, window_seconds):
        """
        Check if a request is allowed for the given username.

        Args:
            username (str): The username to check
            limit_count (int): Maximum number of requests allowed
            window_seconds (int): Time window in seconds

        Returns:
            bool: True if request is allowed, False otherwise
        """
        current_time = time.time()
        user_queue = self.user_requests[username]

        # Remove old requests outside the time window
        while user_queue and current_time - user_queue[0] > window_seconds:
            user_queue.popleft()

        # Check if we're within the limit
        if len(user_queue) < limit_count:
            user_queue.append(current_time)
            return True

        return False

    def get_retry_after(self, username, window_seconds):
        """
        Get the time in seconds until the next request is allowed.

        Args:
            username (str): The username to check
            window_seconds (int): Time window in seconds

        Returns:
            float: Seconds until next request is allowed
        """
        user_queue = self.user_requests[username]
        if not user_queue:
            return 0

        current_time = time.time()
        oldest_request = user_queue[0]
        return max(0, window_seconds - (current_time - oldest_request))

# Initialize the custom rate limiter
rate_limiter = CustomRateLimiter()

def get_username_from_request():
    """
    Extract username from the request payload.

    Returns:
        str: Username if found, otherwise 'anonymous' or IP address as fallback
    """
    username = None

    # Try to get username from JSON data
    if request.is_json:
        data = request.get_json()
        if data and isinstance(data, dict):
            username = data.get('username')

    # Try to get username from form data if not found in JSON
    if not username and request.form:
        username = request.form.get('username')

    # Try to get username from query parameters for GET requests
    if not username and request.args:
        username = request.args.get('username')

    # Fallback to IP address if no username found
    if not username:
        username = request.remote_addr or 'anonymous'

    return username

def rate_limit(limit_count, window_seconds):
    """
    Decorator for rate limiting based on username using distributed rate limiting.

    Args:
        limit_count (int): Maximum number of requests allowed
        window_seconds (int): Time window in seconds
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            username = get_username_from_request()

            # Use distributed rate limiting system
            is_allowed, retry_after, error_msg = check_rate_limit_with_server(
                username, limit_count, window_seconds
            )

            if not is_allowed:
                response_data = {
                    'error': 'Rate limit exceeded',
                    'message': f'Too many requests for user "{username}". Please wait before making another request.',
                    'retry_after': f'{retry_after:.1f} seconds',
                    'username': username
                }

                # Add debug info if there was an error with the rate limit server
                if error_msg:
                    response_data['debug_info'] = f'Fallback used: {error_msg}'

                return jsonify(response_data), 429

            return f(*args, **kwargs)
        return decorated_function
    return decorator

# In-memory cache for storing computed hashes
# Structure: {data_key: {"hash": hash_value, "timestamp": creation_time}}
hash_cache = {}
CACHE_TTL = 300  # Cache time-to-live in seconds (5 minutes)

def clean_expired_cache():
    """Remove expired entries from the cache."""
    current_time = time.time()
    expired_keys = [
        key for key, value in hash_cache.items()
        if current_time - value["timestamp"] > CACHE_TTL
    ]
    for key in expired_keys:
        del hash_cache[key]

def get_cache_key(data):
    """Generate a consistent cache key from the data."""
    if isinstance(data, dict):
        # Convert dict to JSON string for consistent key generation
        return json.dumps(data, sort_keys=True)
    elif isinstance(data, str):
        return data
    else:
        return str(data)

def generate_hash_with_cache_info(data):
    """Generate SHA-256 hash of the provided data with caching and return cache info."""
    # Clean expired cache entries periodically
    clean_expired_cache()

    # Generate cache key
    cache_key = get_cache_key(data)

    # Check if hash is already cached
    if cache_key in hash_cache:
        cached_entry = hash_cache[cache_key]
        # Check if cache entry is still valid
        if time.time() - cached_entry["timestamp"] <= CACHE_TTL:
            return cached_entry["hash"], True  # Hash, is_cached
        else:
            # Remove expired entry
            del hash_cache[cache_key]

    # Generate new hash
    if isinstance(data, dict):
        # Convert dict to JSON string for consistent hashing
        data_str = json.dumps(data, sort_keys=True)
    elif isinstance(data, str):
        data_str = data
    else:
        data_str = str(data)

    hash_value = hashlib.sha256(data_str.encode('utf-8')).hexdigest()

    # Store in cache
    hash_cache[cache_key] = {
        "hash": hash_value,
        "timestamp": time.time()
    }

    return hash_value, False  # Hash, is_cached

@app.route('/', methods=['GET', 'POST'])
@rate_limit(2, 5)  # 2 requests per 5 seconds
def hash_data():
    """Handle both GET and POST requests and return hash of the data."""
    
    if request.method == 'GET':
        # For GET requests, hash the query parameters
        data = dict(request.args)
        if not data:
            data = "No query parameters provided"

        hash_value, is_cached = generate_hash_with_cache_info(data)

        return jsonify({
            'method': 'GET',
            'data': data,
            'hash': hash_value,
            'hash_algorithm': 'SHA-256',
            'cached': is_cached,
            'cache_info': 'Result retrieved from cache' if is_cached else 'Result computed and cached'
        })
    
    elif request.method == 'POST':
        # For POST requests, try to get JSON data first, then form data, then raw data
        if request.is_json:
            data = request.get_json()
        elif request.form:
            data = dict(request.form)
        else:
            data = request.get_data(as_text=True)
            if not data:
                data = "No data provided"

        hash_value, is_cached = generate_hash_with_cache_info(data)

        return jsonify({
            'method': 'POST',
            'data': data,
            'hash': hash_value,
            'hash_algorithm': 'SHA-256',
            'cached': is_cached,
            'cache_info': 'Result retrieved from cache' if is_cached else 'Result computed and cached'
        })

@app.route('/health', methods=['GET'])
@rate_limit(10, 5)  # 10 requests per 5 seconds - more lenient for health checks
def health_check():
    """Simple health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'message': 'Flask HTTP server is running'
    })

@app.route('/cache/stats', methods=['GET'])
@rate_limit(5, 5)  # 5 requests per 5 seconds
def cache_stats():
    """Get cache statistics."""
    # Clean expired entries before reporting stats
    clean_expired_cache()

    current_time = time.time()
    cache_entries = []

    for key, value in hash_cache.items():
        age_seconds = current_time - value["timestamp"]
        cache_entries.append({
            "key_preview": key[:50] + "..." if len(key) > 50 else key,
            "hash_preview": value["hash"][:16] + "...",
            "age_seconds": round(age_seconds, 2),
            "expires_in_seconds": round(CACHE_TTL - age_seconds, 2)
        })

    return jsonify({
        'cache_stats': {
            'total_entries': len(hash_cache),
            'cache_ttl_seconds': CACHE_TTL,
            'entries': cache_entries
        }
    })

@app.route('/register', methods=['POST'])
def register_rate_limit_server():
    """Register a rate limiting server."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        server_id = data.get('server_id')
        host = data.get('host')
        port = data.get('port')
        url = data.get('url')

        if not all([server_id, host, port, url]):
            return jsonify({
                'error': 'Missing required fields',
                'required': ['server_id', 'host', 'port', 'url']
            }), 400

        # Check if server is already registered
        existing_server = next((s for s in rate_limit_servers if s['server_id'] == server_id), None)

        if existing_server:
            # Update existing server info
            existing_server.update({
                'host': host,
                'port': port,
                'url': url,
                'last_seen': time.time()
            })
            message = 'Server registration updated'
        else:
            # Add new server
            rate_limit_servers.append({
                'server_id': server_id,
                'host': host,
                'port': port,
                'url': url,
                'registered_at': time.time(),
                'last_seen': time.time()
            })
            message = 'Server registered successfully'

        return jsonify({
            'status': 'success',
            'message': message,
            'server_id': server_id,
            'total_servers': len(rate_limit_servers)
        })

    except Exception as e:
        return jsonify({'error': f'Registration failed: {str(e)}'}), 500

@app.route('/deregister', methods=['POST'])
def deregister_rate_limit_server():
    """Deregister a rate limiting server."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        server_id = data.get('server_id')

        if not server_id:
            return jsonify({
                'error': 'Missing required field',
                'required': ['server_id']
            }), 400

        # Find and remove the server
        global rate_limit_servers
        original_count = len(rate_limit_servers)
        rate_limit_servers = [
            server for server in rate_limit_servers
            if server['server_id'] != server_id
        ]

        if len(rate_limit_servers) < original_count:
            return jsonify({
                'status': 'success',
                'message': 'Server deregistered successfully',
                'server_id': server_id,
                'total_servers': len(rate_limit_servers)
            })
        else:
            return jsonify({
                'status': 'not_found',
                'message': 'Server not found in registry',
                'server_id': server_id,
                'total_servers': len(rate_limit_servers)
            }), 404

    except Exception as e:
        return jsonify({'error': f'Deregistration failed: {str(e)}'}), 500

@app.route('/rate_limit_servers', methods=['GET'])
def list_rate_limit_servers():
    """List all registered rate limiting servers."""
    current_time = time.time()

    # Clean up servers that haven't been seen in a while (5 minutes)
    global rate_limit_servers
    rate_limit_servers = [
        server for server in rate_limit_servers
        if current_time - server.get('last_seen', 0) < 300
    ]

    return jsonify({
        'total_servers': len(rate_limit_servers),
        'servers': rate_limit_servers
    })

@app.errorhandler(429)
def ratelimit_handler(error):
    """Handle rate limit exceeded errors."""
    # This error handler is now mainly for any other 429 errors
    # Our custom rate limiter handles its own error responses
    return jsonify({
        'error': 'Rate limit exceeded',
        'message': 'Too many requests. Please wait before making another request.',
        'retry_after': 'a few seconds'
    }), 429



if __name__ == '__main__':
    print("Starting Flask HTTP server...")
    # print("Server will be available at: http://localhost:5000")
    # print("Health check endpoint: http://localhost:5000/health")

    app.run(host='0.0.0.0', port=5001, debug=False)
