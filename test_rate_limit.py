#!/usr/bin/env python3
"""
Test script to demonstrate the rate limiting functionality of the Flask server.
"""

import requests
import json
import time

BASE_URL = "http://localhost:5001"  # Updated to match the server port

def test_rate_limiting():
    """Test the rate limiting functionality with username-based limiting."""
    print("Testing Username-Based Rate Limiting (2 requests per 5 seconds)")
    print("=" * 60)

    # Test data with username
    test_data = {"username": "testuser1", "test": "rate_limit_check"}

    # Make requests rapidly to trigger rate limiting
    for i in range(5):
        try:
            print(f"\nRequest {i+1} (username: testuser1):")
            start_time = time.time()

            response = requests.post(BASE_URL, json=test_data)

            end_time = time.time()
            response_time = round((end_time - start_time) * 1000, 2)

            print(f"  Status Code: {response.status_code}")
            print(f"  Response Time: {response_time}ms")

            if response.status_code == 200:
                result = response.json()
                print(f"  Hash: {result.get('hash', 'N/A')[:16]}...")
            elif response.status_code == 429:
                result = response.json()
                print(f"  Error: {result.get('error', 'Rate limit exceeded')}")
                print(f"  Message: {result.get('message', 'Too many requests')}")
                print(f"  Retry After: {result.get('retry_after', 'Unknown')}")
                print(f"  Username: {result.get('username', 'Unknown')}")
            else:
                print(f"  Unexpected status code: {response.status_code}")
                print(f"  Response: {response.text}")

            # Small delay between requests to see the timing
            time.sleep(0.5)

        except requests.exceptions.ConnectionError:
            print(f"  Error: Could not connect to server at {BASE_URL}")
            print("  Make sure the Flask server is running")
            break
        except Exception as e:
            print(f"  Error: {e}")

def test_different_users():
    """Test that different users have separate rate limits."""
    print("\n\nTesting Different Users Have Separate Rate Limits")
    print("=" * 60)

    users = ["user1", "user2", "user3"]

    # Each user makes 2 requests (should all succeed)
    for user in users:
        print(f"\nTesting user: {user}")
        test_data = {"username": user, "test": f"data_for_{user}"}

        for i in range(2):
            try:
                response = requests.post(BASE_URL, json=test_data)
                print(f"  Request {i+1}: Status {response.status_code}")

                if response.status_code == 429:
                    result = response.json()
                    print(f"    Unexpected rate limit for {user}: {result.get('message')}")

            except Exception as e:
                print(f"  Error for {user}: {e}")

    # Now make a third request for user1 (should be rate limited)
    print(f"\nMaking third request for user1 (should be rate limited):")
    test_data = {"username": "user1", "test": "third_request"}
    try:
        response = requests.post(BASE_URL, json=test_data)
        print(f"  Status: {response.status_code}")
        if response.status_code == 429:
            result = response.json()
            print(f"  ✓ Correctly rate limited: {result.get('message')}")
        else:
            print(f"  ✗ Expected rate limit but got: {response.status_code}")
    except Exception as e:
        print(f"  Error: {e}")

def test_rate_limit_recovery():
    """Test that rate limiting recovers after the time window."""
    print("\n\nTesting Rate Limit Recovery")
    print("=" * 60)

    # First, trigger rate limiting with username in query params
    print("1. Triggering rate limit for user 'recovery_user'...")
    for i in range(3):
        try:
            response = requests.get(f"{BASE_URL}?username=recovery_user&test=recovery_{i}")
            print(f"   Request {i+1}: Status {response.status_code}")
        except:
            print(f"   Request {i+1}: Failed")

    # Wait for the rate limit window to reset
    print("\n2. Waiting 6 seconds for rate limit to reset...")
    time.sleep(6)

    # Try again - should work now
    print("\n3. Testing after rate limit reset:")
    try:
        response = requests.get(f"{BASE_URL}?username=recovery_user&test=recovery_after_wait")
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   Success! Hash: {result.get('hash', 'N/A')[:16]}...")
        else:
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")

def test_health_endpoint_rate_limit():
    """Test the health endpoint rate limiting (should be more lenient)."""
    print("\n\nTesting Health Endpoint Rate Limiting (10 requests per 5 seconds)")
    print("=" * 60)
    
    success_count = 0
    rate_limited_count = 0
    
    # Make multiple requests to health endpoint
    for i in range(12):
        try:
            response = requests.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                success_count += 1
                print(f"Request {i+1}: ✓ Success")
            elif response.status_code == 429:
                rate_limited_count += 1
                print(f"Request {i+1}: ✗ Rate limited")
            time.sleep(0.2)  # Small delay
        except Exception as e:
            print(f"Request {i+1}: Error - {e}")
    
    print(f"\nResults:")
    print(f"  Successful requests: {success_count}")
    print(f"  Rate limited requests: {rate_limited_count}")
    print(f"  Expected: ~10 successful, ~2 rate limited")

if __name__ == '__main__':
    print("Flask Username-Based Rate Limiting Test")
    print("Make sure the Flask server is running on port 5001")
    print("Press Ctrl+C to stop the test at any time\n")

    try:
        # Test basic username-based rate limiting
        test_rate_limiting()

        # Test different users have separate limits
        test_different_users()

        # Test rate limit recovery
        test_rate_limit_recovery()

        # Test health endpoint rate limiting
        test_health_endpoint_rate_limit()

        print("\n" + "=" * 60)
        print("Username-based rate limiting tests completed!")

    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
