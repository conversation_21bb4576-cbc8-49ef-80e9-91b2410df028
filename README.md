# Flask HTTP Server with Distributed Rate Limiting

A Flask HTTP server that accepts GET and POST requests and returns a SHA-256 hash of the submitted data. The server features a distributed rate limiting system with dedicated rate limiting servers and intelligent load balancing.

## Features

- **GET and POST Support**: Handles both GET (query parameters) and POST (JSON/form data) requests
- **SHA-256 Hashing**: Returns SHA-256 hash of submitted data
- **Distributed Rate Limiting**: Separate rate limiting servers with automatic registration and load balancing
- **Username-based Distribution**: Users are consistently routed to specific rate limiting servers based on username hash
- **Fallback Support**: Graceful fallback to local rate limiting if distributed servers are unavailable
- **Caching**: In-memory caching of computed hashes with TTL
- **Health Check**: Simple health check endpoint
- **Cache Statistics**: Endpoint to view cache statistics
- **Server Management**: Registration and monitoring of rate limiting servers

## Architecture

The system consists of:

1. **Main Server** (`app.py`): Handles HTTP requests and coordinates with rate limiting servers
2. **Rate Limiting Servers** (`rate_limit_server.py`): Dedicated servers that handle rate limiting logic
3. **Load Balancing**: Users are distributed across rate limiting servers using consistent hashing

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Quick Start with Demo

Run the complete demo:
```bash
python demo_distributed_rate_limit.py
```

This will start:
- Main server on port 5001
- Rate limiting server on port 5002
- Rate limiting server on port 5003
- Demonstrate the system with sample requests

### Manual Setup

1. Start the main server:
```bash
python app.py
```

2. Start rate limiting servers (in separate terminals):
```bash
# Server 1
python rate_limit_server.py --port 5002 --main-server http://localhost:5001

# Server 2
python rate_limit_server.py --port 5003 --main-server http://localhost:5001
```

### Endpoints

#### Main Server (port 5001)
- `GET/POST /` - Main endpoint for hashing data
- `GET /health` - Health check endpoint
- `GET /cache/stats` - Cache statistics
- `POST /register` - Register rate limiting servers
- `GET /rate_limit_servers` - List registered rate limiting servers

#### Rate Limiting Servers (ports 5002+)
- `POST /check_rate_limit` - Check if request is allowed
- `GET /stats` - Rate limiting statistics
- `GET /health` - Health check

### Rate Limiting

- Main endpoint: 2 requests per 5 seconds per username
- Health check: 10 requests per 5 seconds per username
- Cache stats: 5 requests per 5 seconds per username

Rate limiting is based on the `username` field in the request data. Users are consistently routed to the same rate limiting server based on a hash of their username.

## Examples

### GET Request
```bash
curl "http://localhost:5001/?username=alice&message=hello"
```

### POST Request
```bash
curl -X POST http://localhost:5001/ \
  -H "Content-Type: application/json" \
  -d '{"username": "alice", "message": "hello world"}'
```

### Check Registered Servers
```bash
curl http://localhost:5001/rate_limit_servers
```

### View Rate Limiting Stats
```bash
curl http://localhost:5002/stats
```

## Testing

Run the distributed rate limiting tests:
```bash
python test_distributed_rate_limit.py
```

Run the original tests:
```bash
python test_rate_limit.py
python test_cache.py
python test_client.py
```

## Configuration

### Rate Limiting Server Options

```bash
python rate_limit_server.py --help
```

Options:
- `--port`: Port to run the server on (default: 5002)
- `--main-server`: URL of main server to register with

### Scaling

You can run multiple rate limiting servers on different ports. Each will automatically register with the main server and participate in load balancing.

## How It Works

1. **Registration**: Rate limiting servers register with the main server on startup
2. **Distribution**: When a request comes in, the main server hashes the username and selects a rate limiting server
3. **Rate Check**: The main server sends a rate limiting check to the selected server
4. **Fallback**: If the rate limiting server is unavailable, the main server falls back to local rate limiting
5. **Cleanup**: Inactive rate limiting servers are automatically removed from the registry

## Files

- `app.py`: Main Flask application with distributed rate limiting coordination
- `rate_limit_server.py`: Dedicated rate limiting server
- `demo_distributed_rate_limit.py`: Complete demo of the distributed system
- `test_distributed_rate_limit.py`: Comprehensive tests for distributed rate limiting
- `test_client.py`: Original test client for demonstration
- `test_rate_limit.py`: Original rate limiting test script
- `test_cache.py`: Cache functionality test script
- `requirements.txt`: Python dependencies
- `README.md`: This documentation
